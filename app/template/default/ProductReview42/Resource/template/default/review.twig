{#
/*
 * This file is part of the ProductReview plugin
 *
 * Copyright (C) 2016 LOCKON CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
#}
<script src="{{ asset('assets/js/readmore.min.js') }}"></script>
<style>
    @media (max-width: 767px) {
        .item_visual_image_modal .slick-track{
            margin-left: 0;
        }

        .ec-grid2__cell {
            max-height:calc(100% - 90px)
        }
    }

    .slick-slide, .slick-track, .slick-list, .slick-slide div {
        height:100%
    }
</style>
<script>
    $(function() {
        $('#product_review_area').appendTo($('div .review'));
        $('#poin_area').appendTo($('div #poin'));
        $('.read-more-comment').readmore({
            speed: 300,
            collapsedHeight: 52,
            heightMargin: 50,
            moreLink: `<div class="text-[#bababa] float-right mt-[15px]">
                           <a class="text-[12px] underline decoration-3 hover:opacity-50 cursor-pointer">さらに表示</a>
                        </div>`,
            lessLink: `<div class="text-[#bababa] float-right mt-[15px]">
                           <a class="text-[12px] underline decoration-3 hover:opacity-50 cursor-pointer">表示取消し</a>
                        </div>`,
	    });

        // slick Modal
        var itemVisualModal = $('.item_visual_image_modal');

        // bfcache無効化
        $(window).bind('pageshow', function(event) {
            if (event.originalEvent.persisted) {
                location.reload(true);
            }
        });

        // Core Web Vital の Cumulative Layout Shift(CLS)対策のため
        // img タグに width, height が付与されている.
        // 630px 未満の画面サイズでは縦横比が壊れるための対策
        // see https://github.com/EC-CUBE/ec-cube/pull/5023

        var removeSize = function () {
            $('.slide-item').height('');
            $('.slide-item img')
                .removeAttr('width')
                .removeAttr('height')
                .removeAttr('style');
        };
        var slickInitial = function(slick) {
            $('.ec-grid2__cell').fadeIn(1500);
            let baseHeight = $(slick.target).height();
            let baseWidth = $(slick.target).width();
            let rate = baseWidth / baseHeight;

            $('.slide-item').height(baseHeight * rate).addClass('h-full flex items-center'); // 余白を削除する
            // transform を使用することでCLSの影響を受けないようにする
            $('.slide-item img')
                .css(
                    {
                        'transform-origin': 'top left',
                        'transform': 'scaleY(' + rate + ')',
                        'transition': 'transform .1s'
                    }
                );
            // 正しいサイズに近くなったら属性を解除する
            setTimeout(removeSize, 500);
        };

        itemVisualModal.on('init', slickInitial);

        // リサイズ時は CLS の影響を受けないため属性を解除する
        $(window).resize(removeSize);

        // open modal
        $('.ec-modal-review-open').on('click', function() {
            if (itemVisualModal.hasClass('slick-initialized')) {
                itemVisualModal.slick('unslick');
            }

            let index = $(this).attr('data-index') * 1;
            let itemVisualImage = $(this).closest('.item_product_review').find('.item_visual_image').html();
            let itemVisualReview = $(this).closest('.item_product_review').find('.item_visual_review').html();
            $('.ec-grid2__cell').hide();
            $('.ec-modal-review').find('div.item_visual_image_modal').empty().append(itemVisualImage);
            $('.ec-modal-review').find('div.item_visual_review_modal').empty().append(itemVisualReview);

            itemVisualModal.slick({
                dots: false,
                arrows: true,
                initialSlide: index,
                slideToShow: 1,
                prevArrow: $('#topslide-review-prev'),
                nextArrow: $('#topslide-review-next'),
            });

            $('.slideThumb').on('click', function() {
                itemVisualModal.slick('slickGoTo', $(this).attr('data-index') * 1, false);
            })

            $('.ec-modal-review').show();
        });

        $('.ec-modal-review-wrap').on('click', function(e) {
            // モーダル内の処理は外側にバブリングさせない
            e.stopPropagation();
        });
        $('.ec-modal-review-overlay, .ec-modal-review, .ec-modal-review-close').on('click', function() {
            $('.ec-modal-review').hide()
        });
    });
</script>

<!--Product review area-->
<div class="mx-auto px-[5%] md:px-[3%]" id="product_review_area">
    <!-- Title -->
    <div class="flex flex-col font-semibold items-center">
        <h2 class="text-[10px] md:text-[16px] tracking-[.06em] font-semibold">お客様の声</h2>
        <div class="text-[30px] md:text-[46px] tracking-[.06em] font-light din-condensed text-center">
            <p>REVIEW</p>
            <div class="border-b-2 border-[#fee44d] w-full mx-auto"></div>
        </div>
    </div>

    <!-- Review list -->
    {% if ProductReviews %}
        <ul class="max-w-[670px] md:max-w-[640px] flex w-full flex-col pt-[40px] mx-auto">
            {% for ProductReview in ProductReviews %}
                <li class="item_product_review">
                    <!-- Hr -->
                    <div class="my-[20px] h-[1px] bg-[#bababa]"></div>
                    <p class="text-[13px] md:text-[14px] font-semibold">{{ ProductReview.title }}</p>
                    <div class="flex py-[10px]">
                        <!--星の数-->

                        {% set round_avg = ((ProductReview.recommend_level * 2)|round) / 2 %}
                        {% for i in 1.. round_avg %}
                            <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                                <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#ffe600"/>
                            </svg>
                        {% endfor %}
                        {% set round_avg = round_avg + 1 %}
                        {% if round_avg <= 5 %}
                            {% for i in round_avg ..5 %}
                            <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                                <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#eaeaea"/>
                            </svg>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="md:flex pt-[20px] flex-wrap">
                        <div class="flex w-full md:w-full text-[13px] md:text-[14px]">
                            <ul class="flex flex-wrap gap-x-[4%] gap-y-[20px] w-full">
                                {% for ProductReviewImage in ProductReview.ProductReviewImage %}
                                    <li class="max-w-[48%] md:max-w-[22%]">
                                        <img class="ec-modal-review-open cursor-pointer object-cover rounded-2xl w-full aspect-square pb-1" src="{{ asset(ProductReviewImage, 'save_image') }}" data-index="{{ loop.index0 }}">
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="read-more-comment overflow-hidden">
                        <div class="content">
                            <p class="text-[13px] md:text-[14px] mt-[10px] text-justify">{{ ProductReview.comment|nl2br }}</p>
                        </div>
                        <div class="pt-[20px] text-[#666666] flex justify-between ">
                            <div class="flex flex-wrap items-center">
                                <p class="text-[12px] break-words max-w-lg">{{ 'product_review.front.product_detail.name'|trans({ '%name%': ProductReview.reviewer_name }) }}</p>
                                <p class="font-light text-[12px] md:text-[14px] pl-2">{{ ProductReview.create_date|date('Y.m.d') }}</p>
                            </div>
                        </div>
                        <!--レビュー返信-->
                        {% if ProductReview.reply %}
                            <div class=" border-l-2 border-[#bababa] px-[3%] mx-[3%] mt-[10px]">
                                <p class="text-[13px] md:text-[14px] text-[#bababa]">お店からの返信</p>
                                <p class="text-[13px] md:text-[14px] mt-[10px] text-justify">{{ ProductReview.reply|nl2br }}</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- content modal -->
                    <div class="item_visual_image hidden">
                        {% for ProductReviewImage in ProductReview.ProductReviewImage %}
                            <div class="slide-item"><img src="{{ asset(ProductReviewImage, 'save_image') }}" class="object-contain h-full w-full" width="240" height="240" {% if loop.index > 1 %} loading="lazy"{% endif %}></div>
                        {% endfor %}
                    </div>

                    <div class="item_visual_review hidden">
                        <div class="md:hidden pb-[10px] flex flex-row gap-2 overflow-x-scroll">
                            {% for ProductReviewImage in ProductReview.ProductReviewImage %}
                                <a class="cursor-pointer slideThumb w-fit h-fit" data-index="{{ loop.index0 }}">
                                    <img class="rounded aspect-square object-cover min-h-[65px] min-w-[65px] max-h-[65px] max-w-[65px]"
                                        alt="No alt" src="{{ asset(ProductReviewImage, 'save_image') }}" />
                                </a>
                            {% endfor %}
                        </div>
                        <p class="text-[13px] md:text-[14px] font-semibold">{{ ProductReview.title }}</p>
                        <div class="flex py-[10px]">
                            <!--星の数-->

                            {% set round_avg = ((ProductReview.recommend_level * 2)|round) / 2 %}
                            {% for i in 1.. round_avg %}
                                <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                                    <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#ffe600"/>
                                </svg>
                            {% endfor %}
                            {% set round_avg = round_avg + 1 %}
                            {% if round_avg <= 5 %}
                                {% for i in round_avg ..5 %}
                                <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                                    <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#eaeaea"/>
                                </svg>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="content">
                            <p class="text-[13px] md:text-[14px] mt-[10px] text-justify">{{ ProductReview.comment|nl2br }}</p>
                        </div>
                        <div class="pt-[20px] text-[#bababa] flex justify-between ">
                            <div class="flex flex-wrap items-center">
                                <p class="text-[12px] break-words max-w-lg">{{ 'product_review.front.product_detail.name'|trans({ '%name%': ProductReview.reviewer_name }) }}</p>
                                <p class="font-light text-[12px] md:text-[14px] pl-2">{{ ProductReview.create_date|date('Y.m.d') }}</p>
                            </div>
                        </div>
                        <!--レビュー返信-->
                        {% if ProductReview.reply %}
                            <div class=" border-l-2 border-[#bababa] px-[3%] mx-[3%] mt-[10px]">
                                <p class="text-[13px] md:text-[14px] text-[#bababa]">お店からの返信</p>
                                <p class="text-[13px] md:text-[14px] mt-[10px] text-justify">{{ ProductReview.reply|nl2br }}</p>
                            </div>
                        {% endif %}
                        <div class="w-full flex-col hidden md:grid grid-cols-4 md:grid-cols-4 gap-2 justify-between pt-[10px]">
                            {% for ProductReviewImage in ProductReview.ProductReviewImage %}
                                <a class="cursor-pointer slideThumb" data-index="{{ loop.index0 }}">
                                    <img class="rounded-xl aspect-square object-cover h-full"
                                        alt="No alt" src="{{ asset(ProductReviewImage, 'save_image') }}" />
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </li>
            {% endfor %}
        </ul>
    {% else %}
        <p class="text-center pt-8">{{ 'product_review.front.product_detail.no_review'|trans }}</p>
    {% endif %}
</div>
<!--Product review area -->
<!--Poin area-->
{% if Product is defined %}
    <div class="flex items-center pb-[10px]" id="poin_area">
        <a href="{{ url('product_review_detail', { id: Product.id }) }}" class="flex items-center">
            {% if ProductReviews and ProductReviewAvg is defined %}
                {% set round_avg = ((ProductReviewAvg * 2)|round) / 2 %}
                {% for i in 1.. round_avg %}
                    <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                        <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#ffe600"/>
                    </svg>
                {% endfor %}
                {% set round_avg = round_avg + 1 %}
                {% if round_avg <= 5 %}
                    {% for i in round_avg ..5 %}
                        <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                            <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#eaeaea"/>
                        </svg>
                    {% endfor %}
                {% endif %}
            {% else %}
                {% for i in 1 ..5 %}
                    <svg ria-hidden="true" xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[14.4px] md:w-[18.926px] md:h-[18px]" viewBox="0 0 18.926 18">
                        <path id="Path_9069" data-name="Path 9069" d="M357.092,319.311l2.924,5.925,6.539.95-4.732,4.612,1.117,6.512-5.849-3.075-5.849,3.075,1.117-6.512-4.732-4.612,6.539-.95Z" transform="translate(-347.629 -319.311)" fill="#eaeaea"/>
                    </svg>
                {% endfor %}
            {% endif %}
        </a>
        <a href="{{ url('product_review_detail', { id: Product.id }) }}" class="pl-[2%] text-[11px] md:text-[12px] text-[#0033FF]">{{ ProductReviewCount }}件</a>
        <a href="{{ url('product_review_index', { id: Product.id }) }}" class="underline underline-offset-1 pl-[4%] text-[11px] md:text-[12px] text-[#00000]">レビューを書く</a>
    </div>
{% endif  %}
<!--Poin area-->

<!-- Modal review -->
<div class="fixed top-0 left-0 z-[9999] w-full h-full ec-modal-review" style="display: none;">
    <div class="flex justify-center md:items-center w-full h-full bg-[#000] bg-opacity-30 ec-modal-review-overlay">
        <div class="relative bg-[#fff] pb-4 pt-10 md:p-6 md:my-4 mx-auto ec-modal-review-wrap w-full h-[80%] md:w-auto md:h-auto md:mt-0 mt-[65px]">
            <span class="ec-modal-review-close cursor-pointer absolute right-4 top-4 h-[30px] w-[20px]">
                <span class="ec-icon"><img src="{{ asset('assets/icon/cross-dark.svg') }}" alt=""/></span>
            </span>
            <div class="px-4 md:p-4 w-auto overflow-y-scroll overflow-x-hidden md:overflow-hidden h-full">
                <div class="flex flex-col md:flex-row h-full w-full md:h-[480px] md:w-[960px]">
                    <div class="relative h-4-0 w-full md:h-full md:w-[50%] ec-grid2__cell">
                        <div class="item_visual_image_modal overflow-hidden h-full">
                        </div>
                        <button type="button" id="topslide-review-prev" class="absolute top-0 -left-3 z-30 flex items-center justify-center h-full cursor-pointer group focus:outline-none">
                            <span class="hover:opacity-50">
                                <svg xmlns="http://www.w3.org/2000/svg" width="56.163" height="50.163" viewBox="0 0 26.163 26.163">
                                    <path id="Path_681" data-name="Path 681" d="M0,0H18V18"
                                        transform="translate(13.435 25.81) rotate(-135)" fill="none" stroke="#333333" stroke-width="0.5">
                                    </path>
                                </svg>
                            </span>
                        </button>
                        <button type="button" id="topslide-review-next"
                            class="absolute top-0 -right-3 z-30 flex items-center justify-center h-full cursor-pointer group focus:outline-none">
                            <span class="hover:opacity-50">
                                <svg xmlns="http://www.w3.org/2000/svg" width="56.163" height="50.163" viewBox="0 0 26.164 26.163">
                                    <path id="Path_680" data-name="Path 680" d="M0,0H18V18"
                                        transform="translate(12.728 0.354) rotate(45)" fill="none" stroke="#333333" stroke-width="0.5">
                                    </path>
                                </svg>
                            </span>
                        </button>
                    </div>
                    <div class="md:w-[50%] h-full pt-4 md:pt-0 md:pl-4 md:overflow-y-scroll md:overflow-x-hidden item_visual_review_modal">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal review -->