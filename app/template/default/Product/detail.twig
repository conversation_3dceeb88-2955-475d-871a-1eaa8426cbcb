{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% set body_class = 'product_page' %}

{% block stylesheet %}
    <style>
        .ec-productRole__description br:first-child {
            display: none;
        }
    </style>
{% endblock %}

{% block javascript %}
    <script>
        $(function() {
            // bfcache無効化
            $(window).bind('pageshow', function(event) {
                if (event.originalEvent.persisted) {
                    location.reload(true);
                }
            });

            // Core Web Vital の Cumulative Layout Shift(CLS)対策のため
            // img タグに width, height が付与されている.
            // 630px 未満の画面サイズでは縦横比が壊れるための対策
            // see https://github.com/EC-CUBE/ec-cube/pull/5023
            $('.ec-grid2__cell').hide();
            var removeSize = function () {
                $('.slide-item').height('');
                $('.slide-item img')
                    .removeAttr('width')
                    .removeAttr('height')
                    .removeAttr('style');
            };
            var slickInitial = function(slick) {
                $('.ec-grid2__cell').show();
                $('.item_visual').fadeIn(1500);
                $('.slide-item-default').hide();
                var baseHeight = $(slick.target).height();
                var baseWidth = $(slick.target).width();
                var rate = baseWidth / baseHeight;

                $('.slide-item').height(baseHeight * rate); // 余白を削除する
                // transform を使用することでCLSの影響を受けないようにする
                $('.slide-item img')
                    .css(
                        {
                            'transform-origin': 'top left',
                            'transform': 'scaleY(' + rate + ')',
                            'transition': 'transform .1s'
                        }
                    );
                // 正しいサイズに近くなったら属性を解除する
                setTimeout(removeSize, 500);
            };
            $('.item_visual').on('init', slickInitial);
            // リサイズ時は CLS の影響を受けないため属性を解除する
            $(window).resize(removeSize);
            $('.item_visual').slick({
                dots: false,
                arrows: true,
                prevArrow: $('#topslide-prev'),
                nextArrow: $('#topslide-next')
            });

            var selected = 'border-[2px] border-[#333333] box-border';
            $('.slideThumb').on('click', function() {
                var index = $(this).attr('data-index');
                $('.slideThumb img').removeClass(selected);
                $(this).find('img').addClass(selected);

                $('.item_visual').slick('slickGoTo', index, false);

                const productCode = $(this).find('img').data('id');
                if (productCode) {
                    const $colorButton = $('.color-option button#' + productCode);
                    if ($colorButton.length > 0 && !$colorButton.hasClass('outline')) {
                        $colorButton.trigger('click');
                    }
                }
            });

            $('.item_visual').on('afterChange', function(event, slick, currentSlide) {
                $('.slideThumb img').removeClass(selected);
                const $currentThumb = $('.slideThumb[data-index="' + currentSlide + '"]');
                $currentThumb.find('img').addClass(selected);
                const productCode = $currentThumb.find('img').data('id');

                if (productCode) {
                    const $colorButton = $('.color-option button#' + productCode);
                    if ($colorButton.length > 0 && !$colorButton.hasClass('outline')) {
                        $colorButton.trigger('click');
                    }
                }
            });

            $('.color-option button').on('click', function() {
                const $btns = $('.color-option button');
                const $this = $(this);
                const outlineClass = 'outline outline-1 md:outline-2 outline-[#333333] outline-offset-2 md:outline-offset-4';
                const borderClass = 'border border-[#333333]';
                const productCode = $this.attr('id');

                $btns.removeClass(outlineClass).addClass(borderClass);
                $this.addClass(outlineClass).removeClass('border');

                $('#selected-color').text($this.data('color-name'));
                $('#form1 input#ProductClass').val($this.data('product-class-id'));

                const stockUnlimited = $this.data('stock-unlimited');
                const stock = Number($this.data('stock'));
                let stockText = '在庫切れ';

                if (stockUnlimited || stock > 0) {
                    stockText = stock > 0 && stock <= 5
                        ? `在庫状況: <span class="text-[#FF0000] font-semibold ml-1"> 残り${stock}点</span>`
                        : '在庫状況: あり';
                }

                $('#stock-status').html(stockText);
                const showAddCart = stockUnlimited || stock > 0;
                $('.button-add-cart .add-cart').toggleClass('hidden pointer-events-none bg-[#BABABA]', !showAddCart).toggleClass('bg-[#a6011a]', showAddCart).prop('disabled', !showAddCart);
                $('.button-add-cart .sold-out').toggleClass('hidden', showAddCart);
                updateAddToCartButton();

                if (productCode) {
                    $('.slideThumb').each(function() {
                        const $img = $(this).find('img');
                        const thumbProductCode = $img.data('id');

                        if (thumbProductCode == productCode && !$img.hasClass(selected)) {
                            $(this).trigger('click');
                            return false;
                        }
                    });
                }
            });
        });
    </script>
    <script>
        eccube.giftsClassCategories = {
            {% for gift in gifts %}
            "{{ gift.id|escape('js') }}": { "name": "{{ gift.name|escape('js') }}", "classcategory_id1": {{ gift.classCategories1|json_encode()|raw }}, "class_list": {{ class_categories_as_json(gift)|raw }} }{% if loop.last == false %}, {% endif %}
            {% endfor %}
        };

        let giftProductChoiceEl = $('#gift_product_id_choice');
        let giftProductChoices = '';
        $.each(eccube.giftsClassCategories, function(productId, productData) {
            giftProductChoices += '<option value="' + productId + '">' + productData.name + '</option>';
        });
        giftProductChoiceEl.append(giftProductChoices);

        const optionCheckboxes = $('.option-checkbox');
        const maxOptions = {{ Product.ProductOption.optionQuantity ?? 0 }};

        function updateAddToCartButton() {
            if (!optionCheckboxes || !maxOptions) return;

            const checkedCount = optionCheckboxes.filter(':checked').length;

            $('.add-cart')
                .prop('disabled', checkedCount !== maxOptions)
                .toggleClass('pointer-events-none bg-[#BABABA]', checkedCount !== maxOptions)
                .toggleClass('bg-[#a6011a]', checkedCount === maxOptions);
        }

        $(function() {
            if ("{{ Product.ProductOption is not null ? 'true' : 'false' }}" === "true" && maxOptions > 0) {
                optionCheckboxes.on('change', function() {
                    const checkedCount = optionCheckboxes.filter(':checked').length;

                    if (checkedCount > maxOptions && maxOptions > 0) {
                        this.checked = false;
                        alert('最大' + maxOptions + '種類までしか選択できません。');
                    }

                    updateAddToCartButton();
                });

                updateAddToCartButton();
            }

            let giftIdChoiceEl = $('#gift_id_choice');
            $('#gift_product_id_choice').on('change', function(e) {
                let giftChoices = '';
                let productId = $(this).val();

                if (productId && eccube.giftsClassCategories[productId] !== undefined) {
                    // モーダル内の処理は外側にバブリングさせない
                    $.each(eccube.giftsClassCategories[productId].class_list, function(productClassId, productClassData) {
                        if (productClassId === '__unselected2' || (!isNaN(productClassId) && productClassId == {{ Product.gift_type }})) {
                            $.each(productClassData, function(key, value) {
                                if (!value.product_class_id || value.stock_find) {
                                    giftChoices += '<option value="' + value.product_class_id + '">' + value.name + '</option>';
                                }
                            });
                        }
                    });

                    giftIdChoiceEl.html(giftChoices).prop('selectedIndex', 0).change().show();
                    if (giftIdChoiceEl.find('option').first().text()) {
                        giftIdChoiceEl.show();
                        giftIdChoiceEl.next().show();
                    } else {
                        giftIdChoiceEl.hide();
                        giftIdChoiceEl.next().hide();
                    }
                } else {
                    giftIdChoiceEl.hide().val('');
                    giftIdChoiceEl.next().hide();
                }
            });

            $('body').on('click', '.add-cart', function(event) {
                event.preventDefault();

                if ("{{ Product.ProductOption is not null ? 'true' : 'false' }}" === "true" && maxOptions > 0) {
                    updateAddToCartButton();

                    const checkedCount = optionCheckboxes.filter(':checked').length;
                    if (checkedCount !== maxOptions) {
                        alert('オプションを' + maxOptions + '個選択してください。');

                         return false;
                    }
                }

                if ("{{ Product.className2 is null ? 'true' : 'false' }}" === "true" && "{{ Product.className1 }}" == 'カラー' && !$('#form1 input#ProductClass').val()) {
                    alert('{{ '商品の色を選択してください'|trans }}');

                    return false;
                }


                if ($(this).hasClass('add-cart-mobile') && document.querySelectorAll('.add-cart').length > 2) {
                    alert('{{ '商品の種類を選ぶ欄のカートボタンをご利用ください'|trans }}');
                    $('.add-cart').prop('disabled', false);
                    return;
                }

                if (giftProductChoiceEl.val() && !giftIdChoiceEl.val()) {
                    alert(giftProductChoiceEl.find("option").first().text());
                    return;
                }

                // 個数フォームのチェック
                if ($('#quantity').val() < 1) {
                    $('#quantity')[0].setCustomValidity('{{ '1以上で入力してください。'|trans }}');
                    return true;
                } else {
                    $('#quantity')[0].setCustomValidity('');
                }

                $form = $('#form1');

                if ($(this).find('input').val()) {
                    let idProductClass = $(this).find('input').val();
                    $('#form1').find('input#ProductClass').val(idProductClass);
                }

                $.ajax({
                    url: $form.attr('action'),
                    type: $form.attr('method'),
                    data: $form.serialize(),
                    dataType: 'json',
                    beforeSend: function(xhr, settings) {
                        // Buttonを無効にする
                        $('.add-cart').prop('disabled', true);
                    }
                }).done(function(data) {
                    // レスポンス内のメッセージをalertで表示
                    $.each(data.messages, function() {
                        $('#ec-modal-header').addClass('pr-[30px] md:pr-[40px]').text(this);
                    });

                    $('.ec-modal').show()
                    $('body').css('overflow', 'hidden');

                    $.ajax({
                        url: '{{ url('cart_recommend') }}',
                        type: 'GET',
                        dataType: 'html',
                        data: { product_ids: [ {{ Product.id }} ] },
                    }).done(function(html) {
                        $('.cart-recommend').html(html);
                    });

                    // カートブロックを更新する
                    $.ajax({
                        url: "{{ url('block_cart') }}",
                        type: 'GET',
                        dataType: 'html'
                    }).done(function(html) {
                        $('.cart').html(html);
                    });
                }).fail(function(data) {
                    alert('{{ 'カートへの追加に失敗しました。'|trans }}');
                }).always(function(data) {
                    // Buttonを有効にする
                    $('.add-cart').prop('disabled', false);
                });
            });

            $('body').on('click', '.add-favorite', function(event) {
                event.preventDefault();
                event.stopPropagation();

                $.ajax({
                    type: 'POST',
                    url: $(this).closest('form').attr('action'),
                    data: {
                        _token: $(this).closest('form').attr('token-for-anchor'),
                    },
                })
                .done(function(response) {
                   if (response.status === 'success') {
                        var html = $('.status-favorite').html();

                        if(response.action == 'add_favorite') {
                            html = `<svg class="w-5 h-5 md:w-6 md:h-6" xmlns="http://www.w3.org/2000/svg" width="27" height="24" viewBox="0 0 27 24">
                                        <g id="Group_9570" data-name="Group 9570" transform="translate(0 0)">
                                        <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="#a6011a"/>
                                        </g>
                                    </svg>`;
                            alert('商品がお気に入りに正常に追加されました。');
                        } else {
                            html = `<svg class="w-5 h-5 md:w-6 md:h-6" xmlns="http://www.w3.org/2000/svg" width="28" height="25" viewBox="0 0 28 25">
                                        <g id="Group_9572" data-name="Group 9572" transform="translate(0.5 0.5)">
                                        <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="none" stroke="#707070" stroke-width="1"/>
                                        </g>
                                    </svg>`;
                            alert('商品がお気に入りから削除されました。');
                        }

                        $('.status-favorite').html(html);
                    } else {
                        alert('商品をお気に入りに追加するには、ログインしてください。');
                        if (response.redirect_url) {
                            window.location.href = response.redirect_url;
                        }
                    }
                })
                .fail(function(jqXHR) {
                    alert('エラーが発生しました。もう一度お試しください。');
                });;
            });
        });

        $('.ec-modal-wrap').on('click', function(e) {
            // モーダル内の処理は外側にバブリングさせない
            e.stopPropagation();
        });
        $('.ec-modal-close, .ec-inlineBtn--cancel').on('click', function() {
            $('body').css('overflow', 'auto');
            $('.ec-modal').hide();
        });
    </script>
{% endblock %}

{% block main %}

{% set isAlcohol = false %}
{% set isSolarType = (Product.ProductClasses[0]|default({}).SaleType|default({}).id|default(null)) == eccube_config['sale_type_solar'] %}

<!-- Breadcrumb -->
<div class="container max-w-screen-xl mx-auto px-[5%] md:px-[3%]">
    <div class="bg-white py-[7px] flex items-start flex-col">
        {% if Product.ProductCategories|length %}
            {% set listParentId = [] %}
            {% for ProductCategory in Product.ProductCategories %}
                {% set Parent = ProductCategory.Category.Parent %}
                {% for i in 0..10 if Parent %}
                    {% set listParentId = listParentId|merge([Parent.id]) %}
                    {% set Parent = Parent.Parent %}
                {% endfor %}
            {% endfor %}
            {% set done = false %}
            {% for ProductCategory in Product.ProductCategories if not done and ProductCategory.category_id not in listParentId %}
                <ul class="flex flex-wrap items-center text-[12px] text-[#bababa]">
                    {% for Category in ProductCategory.Category.path %}
                        <li class="inline-flex items-center">
                            <a href="{{ url('product_list') }}?category_id%5B%5D={{ Category.id }}" class="hover:opacity-50 cursor-pointer">{{ Category.name }}</a>
                            <span class="mx-1">/</span>
                        </li>
                    {% endfor %}
                </ul>
                {% set done = true %}
            {% endfor %}
        {% else %}
                <ul class="flex flex-wrap items-center text-[12px] text-[#bababa]">
                    <li class="inline-flex items-center">
                        <a href="{{ url('homepage') }}" class="hover:opacity-50">
                            ホーム
                        </a>
                        <span class="mx-1">/</span>
                    </li>
                    <li class="inline-flex items-center">
                        {{ Product.name }}
                    </li>
                </ul>
        {% endif %}
    </div>
</div>

<div class="font-body antialiased text-[#41454c] bg-[#FFFFFF] dark:text-[#b3c3d9] dark:bg-[#000000]">
    <div class="container px-[5%] max-w-screen-xl mx-auto md:pt-[30px] pt-[5px]">
        <div class="max-w-[670px] md:max-w-screen-xl mx-auto">
            <!-- Logo -->
            <div class="w-full md:flex">
                <div class="ec-grid2__cell md:max-w-[600px] md:w-[60%] mx-auto">
                    <div class="w-full">
                        {% set countImage = Product.ProductImage.count %}
                        <div class="relative">
                            <div class="overflow-hidden slide-item-default">
                                <div class="slide-item"><img src="{{ asset(''|no_image_product, 'save_image') }}" class="w-full rounded-2xl " alt="" width="550" height="550"></div>
                            </div>
                            <div class="item_visual overflow-hidden" style="display:none">
                                {% for ProductImage in Product.ProductImage %}
                                    <div class="slide-item"><img src="{{ asset(ProductImage, 'save_image') }}" class="w-full rounded-2xl " alt="{{ loop.first ? Product.name : '' }}" width="550" height="550"{% if loop.index > 1 %} loading="lazy"{% endif %}></div>
                                {% else %}
                                    <div class="slide-item"><img src="{{ asset(''|no_image_product, 'save_image') }}" class="w-full rounded-2xl " alt="{{ loop.first ? Product.name : '' }}" width="550" height="550"></div>
                                {% endfor %}
                            </div>
                            <button type="button" id="topslide-prev" class="absolute top-0 -left-4 md:-left-3 z-30 flex items-center justify-center h-full cursor-pointer group focus:outline-none">
                                <span class="hover:opacity-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="56.163" height="50.163" class="w-[20px] md:w-[56px]" viewBox="0 0 26.163 26.163">
                                        <path id="Path_681" data-name="Path 681" d="M0,0H18V18"
                                            transform="translate(13.435 25.81) rotate(-135)" fill="none" stroke="#333333" stroke-width="0.5">
                                        </path>
                                    </svg>
                                </span>
                            </button>
                            <button type="button" id="topslide-next"
                                class="absolute top-0 -right-4 md:-right-3 z-30 flex items-center justify-center h-full cursor-pointer group focus:outline-none">
                                <span class="hover:opacity-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="56.163" height="50.163" class="w-[20px] md:w-[56px]" viewBox="0 0 26.164 26.163">
                                        <path id="Path_680" data-name="Path 680" d="M0,0H18V18"
                                            transform="translate(12.728 0.354) rotate(45)" fill="none" stroke="#333333" stroke-width="0.5">
                                        </path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                    {% if countImage %}
                        <div class="w-[105.5%] md:w-full flex md:flex-col md:grid md:grid-cols-8 gap-[10px] md:justify-between md:pt-[10px] pt-[5px] overflow-x-auto whitespace-nowrap">
                            {% for ProductImage in Product.ProductImage %}
                                {% set image = asset(ProductImage|no_image_product, 'save_image') %}
                                {% set filename = image|split('/')|last %}
                                {% set id = filename|split(']')|first|split('[')|last %}

                                <a class="cursor-pointer slideThumb w-auto flex-shrink-0" data-index="{{ loop.index0 }}">
                                    <img data-id="{{ id }}" class="rounded-[6px] aspect-square object-cover h-full max-h-[67px] md:max-h-[66px] {% if loop.first %} border-[2px] border-[#333333] box-border {% endif %}"
                                        alt="No alt" src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" />
                                </a>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="md:w-[40%] md:ml-[5%]">
                    <!-- Tag -->
                        {% if Product.Tags is not empty %}
                            <div class="flex flex-wrap flex-row md:gap-[10px] gap-[5px] product-tag-wrap pt-[20px] md:pt-0 items-center">
                                {% for Tag in Product.Tags|sort((a, b) => a.sort_no <=> b.sort_no) %}
                                    <p class="text-[11px] md:text-[14px] px-3 md:h-[25px] h-[20px] flex items-center {% if Tag.name matches '/^[a-zA-Z ]+$/' %}pt-[3px] md:pt-[2px] din-condensed {% endif %}" id="tag-{{ Tag.id }}">{{ Tag.name }}</p>
                                    {% if Tag.id == eccube_config['alcohol_product_tag_id'] %}
                                        {% set isAlcohol = true %}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        {% endif %}

                    <!-- Name - Price - Review -->
                    <div class="mt-[10px] md:mt-[20px] flex-wrap">
                        <h1 class="text-[18px] md:text-[24px] text-[#333333] font-semibold">{{ Product.name }}</h1>
                            <div class="flex flex-wrap items-end pt-[10px] md:pt-[20px]">
                                {% if Product.getPrice01IncTaxMin is not null %}
                                    <p class="text-[18px] md:text-[24px] font-medium pr-2">
                                        <span class="line-through">
                                            {% if Product.hasProductClass -%}
                                                {% if Product.getPrice01IncTaxMin == Product.getPrice01IncTaxMax %}
                                                    {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                                {% else %}
                                                    {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                                    <span class="text-[13px] md:text-[14px] font-medium">円</span>
                                                    ～
                                                    {{ Product.getPrice01IncTaxMax|number_format(0, '.', ',') }}
                                                {% endif %}
                                            {% else %}
                                                {{Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                            {% endif %}
                                            <span class="text-[13px] md:text-[14px] font-medium">円</span>
                                        </span>
                                        <span class="text-[13px] md:text-[14px] font-medium line-through pb-1">（税込）</span>
                                    </p>
                                    <p class="text-[20px] md:text-[34px] font-medium text-[red] tracking-[.06em]">
                                        {% if Product.hasProductClass -%}
                                            {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                            {% else %}
                                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                                <span class="text-[14px] md:text-[16px] font-medium">円</span>
                                                ～
                                                {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                                            {% endif %}
                                        {% else %}
                                            {{Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                        {% endif %}
                                        <span class="text-[14px] md:text-[16px] font-medium">円</span>
                                        <span class="text-[14px] md:text-[16px] font-medium pb-1">（税込）</span>
                                    </p>
                                {% else %}
                                    <p class="text-[20px] md:text-[34px] font-medium">
                                        {% if Product.hasProductClass -%}
                                            {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                            {% else %}
                                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                                <span class="text-[14px] md:text-[16px] font-medium">円</span>
                                                ～
                                                {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                                            {% endif %}
                                        {% else %}
                                            {{Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                        {% endif %}
                                        <span class="text-[14px] md:text-[16px] font-medium">円</span>
                                        <span class="text-[14px] md:text-[16px] font-medium">（税込）</span>
                                    </p>
                                {% endif %}
                                {% if Product.stock_find == 0 %}
                                    <span class="pb-[2px] text-[12px] md:text-[14px] text-[#FF0000] pl-2 font-normal">
                                        Sold out
                                    </span>
                                {% endif %}
                            </div>
                        <div class="items-center pt-[10px] md:pt-[20px] flex-row">
                            {% if has_product_review_plugin %}
                                <div id="poin"></div>
                            {% endif %}
                        </div>
                    </div>
                    {% set gift_text = 'ギフトオプション' %}
                    {% if form.gift_id is defined and gifts|length > 0 and Product.gift_type != 1 %}
                        {% if gifts|first.classCategories1 is defined and gifts|first.classCategories1|length > 0 %}
                            {% for classCategories1Id,classCategories1Name in gifts|first.classCategories1 if classCategories1Id == Product.gift_type %}
                                {% set gift_text = classCategories1Name %}
                            {% endfor %}
                        {% else %}
                            {% set gift_text = repository('Eccube\\Entity\\ClassCategory').find(Product.gift_type).getName() %}
                        {% endif %}
                    {% endif %}

                    <div class="block">
                        {% if Product.hasProductClass -%}
                            {# Form add cart #}
                            <form action="{{ url('product_add_cart', {id:Product.id}) }}" method="post" id="form1" name="form1">
                                {% if form.gift_id is defined and gifts|length > 0 %}
                                    {{ form_widget(form.gift_id, { 'attr': {
                                        'class': 'hidden'
                                    }}) }}
                                    <div class="relative mt-[15px] md:mt-[30px]">
                                        <select id="gift_product_id_choice" class="text-[14px] md:text-[16px] w-full bg-[#fff] border border-[#333333] px-2 md:py-1 h-[35px] md:h-[45px] rounded appearance-none">
                                            <option value="">{{ gift_text }}を選択してください</option>
                                        </select>
                                        <svg class="absolute right-2 top-[18px] md:top-[22px] transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="relative">
                                        <select hidden required id="gift_id_choice" name="gift_id" class="text-[14px] md:text-[16px] w-full bg-[#fff] border border-[#333333] rounded px-2 md:py-1 h-[35px] md:h-[45px] md:mt-2 mt-[15px] appearance-none"></select>
                                        <svg hidden class="absolute right-2 top-[18px] md:top-[22px] transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <a href="https://store.omusu-bee.jp/news/40" class="text-[11px] md:text-[12px] text-[#0033FF] underline hover:opacity-50">ギフトラッピングについて</a>
                                {% endif %}

                                {% if Product.ProductOption is not null %}
                                    <div class="bg-[#f1f1f1] h-[35px] md:h-auto pt-[10px] pb-[10px] items-center flex mt-4 mb-4">
                                        <p class="ml-[5%] text-[14px] md:text-[16px]">{{ Product.ProductOption.optionName }}{% if Product.ProductOption.optionQuantity > 0 %} 下記の種類から{{ Product.ProductOption.optionQuantity }}種類お選びください{% endif %}</p>
                                    </div>
                                    <div class="flex flex-wrap md:gap-3 gap-2 md:gap-x-5 gap-x-4 mb-5">
                                        {% for optionItem in Product.ProductOption.optionItemsArray %}
                                            <div class="option-item">
                                                <label class="flex items-center">
                                                    <input type="checkbox"
                                                        class="option-checkbox mr-2 md:h-5 md:w-5 w-4 h-4"
                                                        name="product_option_items[]"
                                                        value="{{ optionItem }}"
                                                        data-option-name="{{ optionItem }}">
                                                    <span class="text-[14px] md:text-[16px]">{{ optionItem }}</span>
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                <div class="bg-[#f1f1f1] h-[35px] md:h-auto pt-[10px] pb-[10px] items-center flex mt-2">
                                    <p class="ml-[5%] text-[14px] md:text-[16px]">{{ Product.className1 }}{% if Product.className2 %} / {{ Product.className2 }}{% endif %}を選択</p>
                                </div>

                                {% if isAlcohol %}
                                    <div class="flex flex-wrap mt-[15px] md:mt-[30px] md:text-[24px] text-[18px] border px-6 md:py-4 py-1 border-black">
                                        ※20歳未満の者に対しては酒類を<br>　販売しません。
                                    </div>
                                {% endif %}
                                {% if Product.className2 is null and Product.className1 == 'カラー' %}
                                    <div class="mt-4">
                                        <div class="mb-2 md:mb-4 flex items-center">
                                            <span class="text-[16px] md:text-[18px] mr-2">{{ Product.className1 }}: </span>
                                            <span class="text-[16px] md:text-[18px]" id="selected-color">{{ Product.ProductClasses[0].ClassCategory1.name }}</span>
                                        </div>
                                        <div class="flex flex-wrap md:gap-4 gap-2 mb-4">
                                            {% for Class in Product.ProductClasses %}
                                                <div class="color-option">
                                                    <button type="button"
                                                        data-product-class-id="{{ Class.id }}"
                                                        data-color-name="{{ Class.ClassCategory1.name }}"
                                                        data-stock="{{ Class.stock }}"
                                                        data-stock-unlimited="{{ Class.stock_unlimited or Class.stock != 0 ? 'true' : 'false' }}"
                                                        style="background-color: {{ Class.ClassCategory1.backend_name }};"
                                                        id="{{ Class.code }}"
                                                        class="md:w-[30px] md:h-[30px] w-[25px] h-[25px] rounded-full border border-[#333333] hover:outline md:hover:outline-2 hover:outline-1 hover:outline-[#333333] md:hover:outline-offset-4 hover:outline-offset-2"
                                                        >
                                                    </button>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        <div class="mb-4 h-[30px] flex items-center justify-between text-[14px] md:text-[15px] text-[#333333]">
                                            <p class="md:flex flex-wrap">
                                                <span id="stock-status" class="text-[13px] md:text-[14px] flex flex-wrap">
                                                    {% set firstClass = Product.ProductClasses[0] %}
                                                    {% if firstClass.stock_unlimited or firstClass.stock != 0 %}
                                                        {% set stock = firstClass.stock %}
                                                        在庫状況: {{ stock > 0 and stock <= 5 ? ('<span class="text-[#FF0000] font-semibold ml-1">残り' ~ stock ~ '点</span>') | raw : 'あり' }}
                                                    {% else %}
                                                        在庫切れ
                                                    {% endif %}
                                                </span>
                                            </p>
                                            <p class="relative py-[9px] md:pt-[15px] md:pb-[15px] text-[#333333] text-[14px] md:text-[15px]">
                                                数量
                                                {{ form_widget(form.quantity, { 'attr': {
                                                    'class': 'ml-1 h-[30px] md:h-auto w-[65px] pl-[23px] md:py-1 px-1 text-left border-[#707070] rounded-full border border-solid bg-white appearance-none'
                                                }}) }}
                                                <svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                                {{ form_errors(form.quantity) }}
                                            </p>
                                        </div>
                                        <div class="flex flex-wrap mt-[15px] md:mt-[30px] h-[50px] button-add-cart">
                                            <button disabled type="submit" class="add-cart h-[50px] px-[4%] w-full justify-center flex box-border rounded pointer-events-none bg-[#BABABA] items-center hover:opacity-50 text-[17px] md:text-[16px] text-[#ffffff]">
                                                {{ isSolarType ? 'お申し込み' : 'カートに入れる' }}
                                            </button>
                                            <a class="hidden h-[50px] sold-out ml-auto px-[4%] w-full flex justify-center box-border rounded border-[#FF0000] border bg-[#ffffff] items-center">
                                                <p class="text-[14px] md:text-[16px] text-[#FF0000]">Sold out</p>
                                            </a>
                                        </div>
                                    </div>
                                {% else %}
                                    {% for Class in Product.ProductClasses %}
                                        <div class="flex flex-wrap pt-[15px] md:pt-[30px]">
                                            <p class="flex mr-auto flex-col">
                                                <span class="text-[16px] md:text-[18px]">{{ Class.ClassCategory1.name }}{% if Class.ClassCategory2 %} / {{ Class.ClassCategory2.name }}{% endif %}</span>
                                                <span class="text-[13px] md:text-[14px] flex flex-wrap">
                                                {% if Class.stock_unlimited or Class.stock != 0 %}
                                                    {% set stock = Class.stock %}
                                                    在庫状況: {{ stock > 0 and stock <= 5 ? ('<span class="text-[#FF0000] font-semibold ml-1"> 残り' ~ stock ~ '点</span>') | raw : 'あり' }}
                                                {% else %}
                                                    在庫切れ
                                                {% endif %}</span>
                                            </p>

                                            {% if Class.stock_unlimited or Class.stock !=0 %}
                                                <button type="submit" class="add-cart h-[50px] px-[4%] w-[50%] justify-center flex box-border rounded bg-[#a6011a] items-center hover:opacity-50 text-[17px] md:text-[16px] text-[#ffffff]">
                                                    <input class="hidden" value="{{ Class.id }}">
                                                {{ isSolarType ? 'お申し込み' : 'カートに入れる' }}
                                                </button>
                                            {% else %}
                                                <a class="h-[50px] ml-auto px-[4%] w-[50%] flex justify-center box-border rounded border-black border bg-[#ffffff] items-center">
                                                    <p class="text-[14px] md:text-[16px] text-[#FF0000]">Sold out</p>
                                                </a>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                                <div class="hidden">{{ form_rest(form) }}</div>
                            </form>
                        {% else %}
                            {% if form.quantity is defined %}
                                <form action="{{ url('product_add_cart', {id:Product.id}) }}" method="post" id="form1" name="form1">
                                    <div class="h-[30px] flex items-center justify-between text-[14px] md:text-[15px] text-[#333333]">
                                        <p class="md:flex flex-wrap">
                                            <span>在庫状況：</span>
                                            <span>
                                                {% if Product.stock_find %}
                                                    {% set stock = Product.getStockMax() %}
                                                    {{ stock > 0 and stock <= 5 ? ('<span class="text-[#FF0000] font-semibold ml-1">残り' ~ stock ~ '点</span>') | raw : 'あり' }}
                                                {% else %}
                                                    なし
                                                {% endif %}
                                            </span>
                                        </p>
                                        <p class="relative py-[9px] md:pt-[15px] md:pb-[15px] text-[#333333] text-[14px] md:text-[15px]">
                                            数量
                                            {{ form_widget(form.quantity, { 'attr': {
                                                'class': 'ml-1 h-[30px] md:h-auto w-[65px] pl-[23px] md:py-1 px-1 text-left border-[#707070] rounded-full border border-solid bg-white appearance-none'
                                            }}) }}
                                            <svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                            {{ form_errors(form.quantity) }}
                                        </p>
                                    </div>
                                    {% if Product.ProductOption is not null %}
                                        <div class="mb-2 md:mb-4 mt-4">
                                            <span class="text-[16px] md:text-[18px] mr-2">{{ Product.ProductOption.optionName }}: </span>
                                            {% if Product.ProductOption.optionQuantity > 0 %} <span class="text-[14px] md:text-[15px]">下記の種類から{{ Product.ProductOption.optionQuantity }}種類お選びください</span>{% endif %}
                                        </div>
                                        <div class="flex flex-wrap md:gap-3 gap-2 md:gap-x-5 gap-x-4 mb-5">
                                            {% for optionItem in Product.ProductOption.optionItemsArray %}
                                                <div class="option-item">
                                                    <label class="flex items-center">
                                                        <input type="checkbox"
                                                            class="option-checkbox mr-2 md:h-5 md:w-5 w-4 h-4"
                                                            name="product_option_items[]"
                                                            value="{{ optionItem }}"
                                                            data-option-name="{{ optionItem }}">
                                                        <span class="text-[14px] md:text-[16px]">{{ optionItem }}</span>
                                                    </label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.gift_id is defined and gifts|length > 0 %}
                                        {{ form_widget(form.gift_id, { 'attr': {
                                            'class': 'hidden'
                                        }}) }}
                                        <div class="relative mt-[15px] md:mt-[30px]">
                                            <select id="gift_product_id_choice" class="text-[14px] md:text-[16px] w-full bg-[#fff] border border-[#333333] px-2 md:py-1 h-[35px] md:h-[45px] rounded appearance-none">
                                                <option value="">{{ gift_text }}を選択してください</option>
                                            </select>
                                            <svg class="absolute right-2 top-[18px] md:top-[22px] transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="relative">
                                            <select hidden required id="gift_id_choice" name="gift_id" class="text-[14px] md:text-[16px] w-full bg-[#fff] border border-[#333333] rounded px-2 md:py-1 h-[35px] md:h-[45px] md:mt-2 mt-[15px] appearance-none"></select>
                                            <svg hidden class="absolute right-2 top-[18px] md:top-[22px] transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <a href="https://store.omusu-bee.jp/news/40" class="text-[11px] md:text-[12px] text-[#0033FF] underline hover:opacity-50">ギフトラッピングについて</a>
                                    {% endif %}

                                    {% if isAlcohol %}
                                        <div class="flex flex-wrap mt-[15px] md:mt-[30px] md:text-[24px] text-[18px] border px-6 md:py-4 py-1 border-black">
                                            ※20歳未満の者に対しては酒類を<br>　販売しません。
                                        </div>
                                    {% endif %}

                                    <div class="flex flex-wrap mt-[15px] md:mt-[30px] h-[50px]">
                                        <button type="submit" class="add-cart px-[4%] justify-center w-full flex box-border rounded bg-[#a6011a] items-center hover:opacity-50 text-[17px] text-[#ffffff]">
                                            {{ isSolarType ? 'お申し込み' : 'カートに入れる' }}
                                        </button>
                                    </div>
                                    <div class="hidden">{{ form_rest(form) }}</div>
                                </form>
                            {% else %}
                                   <div class="flex flex-wrap mt-[15px] md:mt-[30px] h-[50px]">
                                        {% if Product.stock_find == 0  %}
                                            <a class="ml-auto px-[4%] w-full justify-center flex box-border rounded border-[#FF0000] border bg-[#ffffff] items-center">
                                                <p class="text-[16px] text-[#FF0000]">Sold out</p>
                                            </a>
                                        {% endif %}
                                    </div>
                            {% endif %}
                        {% endif %}

                        <div class="flex gap-[10px] w-full mt-[15px] md:mt-[30px]">
                            <!-- Favorite -->
                            {% if BaseInfo.option_favorite_product %}
                                <form action="{{ url('product_add_favorite', {id:Product.id}) }}" method="post" class="w-1/2" {{ csrf_token_for_anchor() }}>
                                    <div class="relative flex flex-wrap md:h-[50px] h-[40px] w-full">
                                        <button id="favorite" class="add-favorite cursor-pointer ml-auto px-[4%] w-full justify-center flex box-border rounded-full border-[#333333] border bg-[#ffffff] items-center hover:opacity-50">
                                            <span class="md:text-[16px] text-[12px] text-[#333333]">お気に入り</span>
                                            <span class="status-favorite absolute right-5 top-[11px] md:top-[15px] z-30 flex items-center">
                                                {% if is_favorite == false %}
                                                    <svg class="w-5 h-5 md:w-6 md:h-6" xmlns="http://www.w3.org/2000/svg" width="28" height="25" viewBox="0 0 28 25">
                                                        <g id="Group_9572" data-name="Group 9572" transform="translate(0.5 0.5)">
                                                        <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="none" stroke="#707070" stroke-width="1"/>
                                                        </g>
                                                    </svg>
                                                {% else %}
                                                    <svg class="w-5 h-5 md:w-6 md:h-6" xmlns="http://www.w3.org/2000/svg" width="27" height="24" viewBox="0 0 27 24">
                                                        <g id="Group_9570" data-name="Group 9570" transform="translate(0 0)">
                                                        <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="#a6011a"/>
                                                        </g>
                                                    </svg>
                                                {% endif %}
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            {% endif %}

                            <div class="fixed top-0 left-0 z-[99999] w-full h-full ec-modal" style="display: none;">
                                <div class="flex justify-center items-center w-full h-full bg-[#000] bg-opacity-30">
                                    <div class="relative bg-[#fff] m-5 w-[100%] md:w-[50%] md:my-4 mx-auto md:h-[80%] overflow-hidden h-full">
                                        <div class="text-center flex justify-center border  py-4 px-2">
                                            <span id="ec-modal-header">{{ 'カートに追加しました。'|trans }}</span>
                                            <span class="cursor-pointer absolute right-5 h-[30px] w-[20px] ec-modal-close">
                                                <span class="ec-icon"><img src="{{ asset('assets/icon/cross-dark.svg') }}" alt=""/></span>
                                            </span>
                                        </div>
                                        <div class="text-center px-4 h-full overflow-auto pb-[50px]">
                                            <div class="flex flex-col justify-center items-center pt-5">
                                                <a href="{{ url('cart') }}" class="shadow-lg text-center w-full md:w-[50%] rounded text-[#fff] bg-[#a6011a] text-[17px] md:text-[16px] py-3 mt-[10px]">{{ 'カートを見る'|trans }}</a>
                                                <button class="border border-[#333333] box-border shadow-lg ec-modal-close text-center w-full md:w-[50%] rounded text-[#000] bg-[#fff] text-[17px] md:text-[16px] py-3 mt-[10px]">{{ 'ショッピングを続ける'|trans }}</button>
                                            </div>
                                            <div class="cart-recommend"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <a href="{{ url('contact', {product_id:Product.id}) }}" class="w-1/2 md:py-[15px] py-[5px] md:h-[50px] h-[40px] flex box-border rounded-full border-[#333333] border bg-[#ffffff] justify-center items-center hover:opacity-50 cursor-pointer">
                                <p class="text-[12px] md:text-[16px] text-[#333333]">商品のお問い合わせ</p>
                            </a>
                        </div>
                        {% if Product.description_detail %}
                            <h2 class="mt-5 font-semibold">商品の特徴</h2>
                            <div class="mt-5 ec-productRole__description font-medium text-[11px]">
                                {{ Product.description_detail|raw|nl2br }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="md:max-w-screen-sm md:mx-auto mx-[5%]">
            {% if Product.freearea %}
                <h2 class="mt-5 font-semibold">商品説明</h2>
                <div class="mt-10">
                    {{ include(template_from_string(Product.freearea)) }}
                </div>
            {% endif %}
            <!-- INFORMATION -->
                {% set product_code = Product.code_min ~ (Product.code_min != Product.code_max ? ' ～ ' ~ Product.code_max : '') %}
                {% set product_code_pair = ['商品コード', product_code] %}

                {% set spec_with_code = spec | filter(v => v[0] != '商品コード') %}
                {% if spec | length %}
                    {% set spec_with_code = spec_with_code | slice(0, 1) | merge([product_code_pair]) | merge(spec_with_code | slice(1)) %}
                {% else %}
                    {% set spec_with_code = [product_code_pair] %}
                {% endif %}

                <div class="mt-10 flex flex-wrap items-end">
                    <h2 class="whitespace-nowrap mb-3">
                        <span class="pr-[5px] din-condensed text-[19px] md:text-[26px]">INFORMATION</span>
                        <span class="text-[11px] md:text-[12px] font-semibold">インフォメーション</span>
                    </h2>
                    <div class="border-2 border-[#bababa] rounded-2xl w-full">
                        <table class="border-2 border-collapse rounded-2xl overflow-hidden w-full">
                            <tbody>
                                {% for value in spec_with_code %}
                                    <tr class="border-2 border-[#bababa] rounded-2xl">
                                        <td class="p-[2%] whitespace-nowrap w-1/4 border-2 border-[#bababa] bg-[#ebebeb] text-[12px] md:text-[16px]">
                                            {{ value[0] }}
                                        </td>
                                        <td class="p-[2%] border-2 border-[#bababa] text-[11px] md:text-[14px]">
                                            {% if value|length > 1 %}{{ value[1] }}{% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

            {% if Product.freeareamiddle %}
                <div class="mt-10">
                    {{ include(template_from_string(Product.freeareamiddle)) }}
                </div>
            {% endif %}

            {% if Product.freeareabottom %}
                <div class="mt-10">
                    {{ include(template_from_string(Product.freeareabottom)) }}
                </div>
            {% endif %}

            {% if isAlcohol %}
                <div class="mt-10">
                    <p class="text-left w-full flex-wrap text-[15px] md:text-[18px] font-semibold">酒類販売管理者標識</p>
                    <p class="flex text-[13px] md:text-[14px] pt-[5px] w-full flex"><span class="min-w-[105px]">販売場の所在地：</span><span>東京都千代田区霞が関三丁目2 番5 号 霞が関ビル6 階<br>OMUSUbee オンラインストア</span></p>
                    <p class="text-left text-[13px] md:text-[14px] pt-[5px] w-full">酒類販売管理者：松田昌工</p>
                    <p class="text-left text-[13px] md:text-[14px] pt-[5px] w-full">酒類販売管理研修受講年月日：2024年7月18日</p>
                    <p class="text-left text-[13px] md:text-[14px] pt-[5px] w-full">次回研修の受講期限：2027年7月17日</p>
                    <p class="text-left text-[13px] md:text-[14px] pt-[5px] w-full">研修実施団体名：堺小売酒販組合</p>
                </div>
            {% endif %}
        </div>

        <!-- Hr -->
        <div class="max-w-[960px] w-full mx-auto my-[50px] md:my-[80px] md:px-[3%] lg:px-0">
            <div class="bg-[#707070] h-[1px]"></div>
        </div>
    </div>
    <div class="hidden cart-favorite w-[100%] md:w-[180px] px-[5%] pb-[35px] md:p-[10px] right-0 md:right-2 bottom-0 md:bottom-[20px] fixed flex flex-nowrap justify-between md:flex-col z-30 gap-[10px] filter drop-shadow-[0px_10px_7.5px_rgba(0,0,0,0.3)]">
        <p class="add-cart add-cart-mobile cursor-pointer w-full md:w-full h-[50px] md:h-[40px] px-[4%] justify-center flex box-border rounded bg-[#a6011a] items-center hover:opacity-50 text-[17px] text-[#ffffff]">
            {{ isSolarType ? 'お申し込み' : 'カートに入れる' }}
        </p>
        <form action="{{ url('product_add_favorite', {id:Product.id}) }}" method="post" class="w-[50px] flex-shrink-0 md:w-full" id="form-favorite" {{ csrf_token_for_anchor() }}>
            <div class="relative flex flex-wrap md:mt-[10px] h-[50px] md:h-[40px]">
                <p class="add-favorite cursor-pointer ml-auto px-[4%] w-full justify-center flex box-border rounded border-[#333333] border bg-[#ffffff] items-center hover:opacity-50">
                    <span class="text-[16px] text-[#333333] hidden md:block">お気に入り</span>
                    <span class="status-favorite absolute right-[15px] top-[17px] md:right-3 md:top-[9px] z-30 flex items-center">
                        {% if is_favorite == false %}
                            <svg class="w-5 h-5 md:w-5 md:h-5" xmlns="http://www.w3.org/2000/svg" width="28" height="25" viewBox="0 0 28 25">
                                <g id="Group_9572" data-name="Group 9572" transform="translate(0.5 0.5)">
                                    <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="none" stroke="#707070" stroke-width="1"/>
                                </g>
                            </svg>
                        {% else %}
                            <svg class="w-5 h-5 md:w-6 md:h-6" xmlns="http://www.w3.org/2000/svg" width="27" height="24" viewBox="0 0 27 24">
                                <g id="Group_9570" data-name="Group 9570" transform="translate(0 0)">
                                <path id="Path_611" data-name="Path 611" d="M167.57,185.213c-1.244-1.106-6.052-5.649-8.473-8.249-2.522-2.72-4.976-5.679-4.976-9.684a6.3,6.3,0,0,1,6.557-6.067,6.99,6.99,0,0,1,6.926,4.692h.034a6.946,6.946,0,0,1,6.927-4.692,6.3,6.3,0,0,1,6.557,6.067c0,4-2.455,6.964-4.977,9.684-2.421,2.6-7.263,7.143-8.507,8.249Z" transform="translate(-154.12 -161.213)" fill="#a6011a"/>
                                </g>
                            </svg>
                        {% endif %}
                    </span>
                </p>
            </div>
        </form>
    </div>
</div>

<!-- REVIEW -->
{% if has_product_review_plugin %}
    <div class="review"></div>
    <div class="px-[5%]">
        <div class="mx-auto max-w-[670px] md:max-w-[640px]">
            <div class="flex justify-end items-center pt-8 md:pt-[40px]">
                <a href="{{ url('product_review_detail', { id: Product.id }) }}" class="flex items-center hover:opacity-50">
                    <p class="font-light tracking-[.06em] pr-[10px] din-condensed text-[18px] md:text-[22px] din-condensed">MORE</p>
                    <img class="h-[37px] w-[37px] md:w-[45px] md:h-[45px]" src="{{ asset('assets/img/common/btn-more.svg') }}" loading="lazy">
                </a>
            </div>
            <div class="md:mx-auto flex mx-[5vw] justify-center items-center pt-[60px]">
                <a href="{{ url('product_review_index', { id: Product.id }) }}" type="button"
                    class="text-center h-[50px] px-[4%] w-[220px] md:w-[325px] justify-center flex rounded bg-[#a6011a] items-center hover:opacity-50">
                    <p class="text-[12px] md:text-[16px] text-[#ffffff]">レビューを書く</p>
                </a>
            </div>
        </div>
    </div>
    <!-- Hr -->
    <div class="max-w-[960px] w-full mx-auto my-[50px] md:my-[80px] px-[5%] md:px-[3%] lg:px-0">
        <div class="bg-[#707070] h-[1px]"></div>
    </div>
{% endif %}

<!-- RECOMMEND -->
{{ include('/Block/recommend_product_block.twig', {group: Product.recommend_group}, ignore_missing = true) }}

<!-- CHECK -->
{{ include('/Block/refine_check_item.twig', ignore_missing = true) }}

<!-- RANKING -->
{{ include('/Block/order_by_sale.twig', ignore_missing = true) }}

{% endblock %}
