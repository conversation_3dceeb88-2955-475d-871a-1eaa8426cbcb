{#
 * This file is part of the Recommend Product plugin
 *
 * Copyright (C) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
#}

<!-- CART RECOMMEND -->
{% if products|length %}
    <div class="max-w-[960px] w-full mx-auto pb-10">
        <div class="bg-[#bababa] h-[1px]"></div>
    </div>
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">あとで買う</p>
    <div class="mx-auto w-full max-w-[960px] pb-10">
        <!-- List item -->
        <ul class="flex flex-wrap gap-x-[4%] max-w-[960px] m-auto mt-[5px]">
            {% for FavoriteProduct in products %}
                {% set Product = FavoriteProduct.Product %}
                <li class="relative max-w-[48%] md:max-w-[22%] py-3 {% if loop.index > 10 %}hidden md:block{% endif %}">
                    {{ include('/Product/_part_product.twig', {Product: Product}) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}
