<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Customize\Controller\Admin\Product;

use Eccube\Entity\Product;
use Eccube\Util\CacheUtil;
use Eccube\Entity\Category;
use Eccube\Event\EventArgs;
use Eccube\Entity\ProductTag;
use Eccube\Event\EccubeEvents;
use Eccube\Entity\ProductCategory;
use Eccube\Repository\TagRepository;
use Eccube\Service\CsvExportService;
use Eccube\Entity\Master\ProductStatus;
use Eccube\Controller\AbstractController;
use Eccube\Repository\CategoryRepository;
use Eccube\Form\Type\Admin\CategoryType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints as Assert;


class CategoryController extends AbstractController
{
    /**
     * @var CategoryRepository
     */
    protected $categoryRepository;

    /**
     * @var TagRepository
     */
    protected $tagRepository;

    /**
     * CategoryController constructor.
     *
     * @param CsvExportService $csvExportService
     * @param CategoryRepository $categoryRepository
     */
    public function __construct(
        CategoryRepository $categoryRepository,
        TagRepository $tagRepository
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->tagRepository = $tagRepository;
    }

    /**
     * @Route("/%eccube_admin_route%/product/category", name="admin_product_category", methods={"GET", "POST"})
     * @Route("/%eccube_admin_route%/product/category/{parent_id}", requirements={"parent_id" = "\d+"}, name="admin_product_category_show", methods={"GET", "POST"})
     * @Route("/%eccube_admin_route%/product/category/{id}/edit", requirements={"id" = "\d+"}, name="admin_product_category_edit", methods={"GET", "POST"})
     * @Template("@admin/Product/category.twig")
     */
    public function index(Request $request, CacheUtil $cacheUtil, $parent_id = null, $id = null)
    {
        if ($parent_id) {
            /** @var Category $Parent */
            $Parent = $this->categoryRepository->find($parent_id);
            if (!$Parent) {
                throw new NotFoundHttpException();
            }
        } else {
            $Parent = null;
        }
        if ($id) {
            $TargetCategory = $this->categoryRepository->find($id);
            if (!$TargetCategory) {
                throw new NotFoundHttpException();
            }
            $Parent = $TargetCategory->getParent();
        } else {
            $TargetCategory = new \Eccube\Entity\Category();
            $TargetCategory->setParent($Parent);
            if ($Parent) {
                $TargetCategory->setHierarchy($Parent->getHierarchy() + 1);
            } else {
                $TargetCategory->setHierarchy(1);
            }
        }

        $Categories = $this->categoryRepository->getList($Parent);

        // ツリー表示のため、ルートからのカテゴリを取得
        $TopCategories = $this->categoryRepository->getList(null);

        $builder = $this->formFactory
            ->createBuilder(CategoryType::class, $TargetCategory);

        $builder->add('tag_id', IntegerType::class, [
            'mapped' => false,
            'required' => false,
            'constraints' => [
                new Assert\Regex([
                    'pattern' => '/^\d*$/',
                    'message' => 'タグIDは数字で入力してください。',
                ]),
            ],
            'attr' => [
                'placeholder' => 'タグIDを入力',
            ],
        ]);

        $event = new EventArgs(
            [
                'builder' => $builder,
                'Parent' => $Parent,
                'TargetCategory' => $TargetCategory,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::ADMIN_PRODUCT_CATEGORY_INDEX_INITIALIZE);

        $form = $builder->getForm();

        $forms = [];
        foreach ($Categories as $Category) {
            $forms[$Category->getId()] = $this->formFactory
                ->createNamed('category_' . $Category->getId(), CategoryType::class, $Category);
        }

        if ($request->getMethod() === 'POST') {
            $form->handleRequest($request);
            if ($form->isSubmitted() && $form->isValid()) {
                if ($this->eccubeConfig['eccube_category_nest_level'] < $TargetCategory->getHierarchy()) {
                    throw new BadRequestHttpException();
                }
                log_info('カテゴリ登録開始', [$id]);

                $this->categoryRepository->save($TargetCategory);

                log_info('カテゴリ登録完了', [$id]);

                // $formが保存されたフォーム
                // 下の編集用フォームの場合とイベント名が共通のため
                // このイベントのリスナーではsubmitされているフォームを判定する必要がある
                $event = new EventArgs(
                    [
                        'form' => $form,
                        'Parent' => $Parent,
                        'TargetCategory' => $TargetCategory,
                    ],
                    $request
                );
                $this->eventDispatcher->dispatch($event, EccubeEvents::ADMIN_PRODUCT_CATEGORY_INDEX_COMPLETE);

                $this->addSuccess('admin.common.save_complete', 'admin');

                $cacheUtil->clearDoctrineCache();

                if ($Parent) {
                    return $this->redirectToRoute('admin_product_category_show', ['parent_id' => $Parent->getId()]);
                } else {
                    return $this->redirectToRoute('admin_product_category');
                }
            }

            foreach ($forms as $editForm) {
                $editForm->handleRequest($request);
                if ($editForm->isSubmitted() && $editForm->isValid()) {
                    $this->categoryRepository->save($editForm->getData());

                    // $editFormが保存されたフォーム
                    // 上の新規登録用フォームの場合とイベント名が共通のため
                    // このイベントのリスナーではsubmitされているフォームを判定する必要がある
                    $event = new EventArgs(
                        [
                            'form' => $form,
                            'editForm' => $editForm,
                            'Parent' => $Parent,
                            'TargetCategory' => $editForm->getData(),
                        ],
                        $request
                    );

                    $this->eventDispatcher->dispatch($event, EccubeEvents::ADMIN_PRODUCT_CATEGORY_INDEX_COMPLETE);

                    $this->addSuccess('admin.common.save_complete', 'admin');

                    $cacheUtil->clearDoctrineCache();

                    if ($Parent) {
                        return $this->redirectToRoute('admin_product_category_show', ['parent_id' => $Parent->getId()]);
                    } else {
                        return $this->redirectToRoute('admin_product_category');
                    }
                }
            }
        }

        $formViews = [];
        $formErrors = [];
        foreach ($forms as $key => $value) {
            $formViews[$key] = $value->createView();
            $formErrors[$key]['count'] = $value->getErrors(true)->count();
        }

        $Ids = [];
        if ($Parent && $Parent->getParents()) {
            foreach ($Parent->getParents() as $item) {
                $Ids[] = $item['id'];
            }
        }
        $Ids[] = intval($parent_id);

        return [
            'form' => $form->createView(),
            'Parent' => $Parent,
            'Ids' => $Ids,
            'Categories' => $Categories,
            'TopCategories' => $TopCategories,
            'TargetCategory' => $TargetCategory,
            'forms' => $formViews,
            'error_forms' => $formErrors,
        ];
    }

    public function getCategoryIds(Category $category, &$result = [])
    {
        $result[] = $category->getId();
        foreach ($category->getChildren() as $child) {
            $this->getCategoryIds($child, $result);
        }

        return $result;
    }

    /**
     * @Route("/%eccube_admin_route%/product/category/tag/add", name="admin_product_category_tag_add", methods={"POST"})
     */
    public function addTagToCategory(Request $request, CacheUtil $cacheUtil)
    {
        $tagId = $request->request->get('admin_category')['tag_id'] ?? null;
        $categoryId = $request->request->get('category_id') ?? null;

        try {
            $tag = $this->tagRepository->find($tagId);
            if (!$tag || !$categoryId) {
                throw new NotFoundHttpException();
            }

            $category = $this->categoryRepository->find($categoryId);
            $categoryIds = $this->getCategoryIds($category);

            // 未タグ付け商品を取得してタグ追加
            $qb = $this->entityManager->createQueryBuilder()
                ->select('prod')
                ->from(Product::class, 'prod')
                ->innerJoin(ProductCategory::class, 'pc', 'WITH', 'prod.id = pc.product_id')
                ->leftJoin(ProductTag::class, 'pt', 'WITH', 'pt.Product = prod AND pt.Tag = :tag')
                ->where('pc.category_id IN (:categoryIds)')
                ->andWhere('pt.id IS NULL')
                ->andWhere('prod.Status = :status')
                ->groupBy('prod.id')
                ->setParameter('categoryIds', $categoryIds)
                ->setParameter('tag', $tag)
                ->setParameter('status', ProductStatus::DISPLAY_SHOW);

            foreach ($qb->getQuery()->getResult() as $product) {
                $this->entityManager->persist((new ProductTag())->setProduct($product)->setTag($tag));
            }

            $this->entityManager->flush();
            $this->addSuccess('追加される', 'admin');
            $cacheUtil->clearDoctrineCache();
        } catch (\Exception $e) {
            log_error('カテゴリにタグ追加エラー', [
                'tag_id' => $tagId,
                'category_id' => $categoryId,
                'error' => $e->getMessage(),
            ]);
            $this->addError('admin.common.save_error', 'admin');
        }

        return $this->redirectToRoute('admin_product_category_show', ['parent_id' => $categoryId]);
    }

    /**
     * @Route("/%eccube_admin_route%/product/category/tag/remove", name="admin_product_category_tag_remove", methods={"POST"})
     */
    public function removeTagFromCategory(Request $request, CacheUtil $cacheUtil)
    {
        $tagId = $request->request->get('admin_category')['tag_id'] ?? null;
        $categoryId = $request->request->get('category_id') ?? null;

        try {
            $tag = $this->tagRepository->find($tagId);
            if (!$tag || !$categoryId) {
                throw new NotFoundHttpException();
            }

            $category = $this->categoryRepository->find($categoryId);
            $categoryIds = $this->getCategoryIds($category);

            // タグ付け商品を取得してタグ削除
            $qb = $this->entityManager->createQueryBuilder()
                ->select('pt')
                ->from(ProductTag::class, 'pt')
                ->innerJoin('pt.Product', 'prod')
                ->innerJoin(ProductCategory::class, 'pc', 'WITH', 'prod.id = pc.product_id')
                ->where('pc.category_id IN (:categoryIds)')
                ->andWhere('pt.Tag = :tag')
                ->setParameter('categoryIds', $categoryIds)
                ->setParameter('tag', $tag);

            foreach ($qb->getQuery()->getResult() as $productTag) {
                $this->entityManager->remove($productTag);
            }

            $this->entityManager->flush();
            $this->addSuccess('削除される', 'admin');
            $cacheUtil->clearDoctrineCache();
        } catch (\Exception $e) {
            log_error('カテゴリからタグ削除エラー', [
                'tag_id' => $tagId,
                'category_id' => $categoryId,
                'error' => $e->getMessage(),
            ]);
            $this->addError('admin.common.delete_error', 'admin');
        }

        return $this->redirectToRoute('admin_product_category_show', ['parent_id' => $categoryId]);
    }
}
