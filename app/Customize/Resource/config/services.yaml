parameters:
    # 割引率 CSV
    discount_tag_id: { 10: 87, 20: 90, 25: 91, 30: 92, 35: 93, 40: 94, 45: 95, 50: 96, 55: 97, 60: 98, 65: 99, 70: 100, 75: 101, 80: 102, 85: 103, 90: 104, 5: 105, 15: 106 }
    # 酒類商品のタグID
    alcohol_product_tag_id: 82
    # クーポン対象外
    not_coupons_eligible: 108
    # タキャンペーン対象外商品のタグID
    campaign_exclude_tag_id: 86
    # 対象外商品基本ポイント率
    basic_point_rate: 1
    # 商品地域
    eccube_product_area: ['北陸', '東北', '近畿', '中国・四国', '関東甲信', '九州', '東海']
    # お問い合わせの内容選択
    eccube_contact_type: ['ご注文内容の確認・変更について', 'キャンセルに関するお問い合わせ', '返品・交換に関するお問い合わせ', '商品に関するお問い合わせ', 'ポイントに関するお問い合わせ', 'その他OMUSUbeeに関するお問い合わせ']
    # PayPay決済
    gmo_payment_gateway.pay_type.pay_pay: 45
    # ソーラー販売種別
    sale_type_solar: 110
    # 販売種別のカスタマイズ料金設定
    sale_type_fee:
        # 販売種別のグループ設定 [通常(仙台出荷：ゆうパック), 通常(仙台出荷：レターパック)]
        delivery_fee_one:
            - { group: [5, 7], sale_type: 5 }
        # 送料無料（金額）
        delivery_free:
            - { group: [1, 8, 20, 100, 101, 102], subtotal_greater_than: 7700 }
    hotel_ranking_point_rate: { 1: 3, 2: 5, 3: 7, 4: 10, 5: 10, 6: 15 }
services:
    # HESTAポイント連携
    Customize\Service\EntityProxyService:
        decorates: Eccube\Service\EntityProxyService
        arguments:
            - '@doctrine.orm.default_entity_manager'
            - '@service_container'
            - '@Customize\Service\EntityProxyService.inner'
    Customize\Service\PointHelper:
        public: false
        autowire: true
        decorates: Eccube\Service\PointHelper
    # 納品書に「二十歳未満の者の飲酒防止に関する表示」記載
    Customize\Service\OrderPdfService:
        public: false
        autowire: true
        decorates: Eccube\Service\OrderPdfService
    # Remember Meログイン済の場合も注文できる
    Customize\Service\OrderHelper:
        public: false
        autowire: true
        decorates: Eccube\Service\OrderHelper
    # 既存Service拡張
    Customize\Service\Plugin\GmoPaymentGateway42\PaymentHelperGanb:
        public: false
        autowire: true
        decorates: Plugin\GmoPaymentGateway42\Service\PaymentHelperGanb
    Customize\Service\Plugin\GmoPaymentGateway42\PaymentHelperReceive:
        public: false
        autowire: true
        decorates: Plugin\GmoPaymentGateway42\Service\PaymentHelperReceive
        arguments:
            $shoppingPurchaseFlow: '@eccube.purchase.flow.shopping'
    Customize\Service\Plugin\GmoPaymentGateway42\PaymentHelperAdmin:
        public: false
        autowire: true
        decorates: Plugin\GmoPaymentGateway42\Service\PaymentHelperAdmin
    Customize\Service\Plugin\SalesReport42\SalesReportService:
        public: false
        autowire: true
        decorates: Plugin\SalesReport42\Service\SalesReportService
    Customize\Service\Plugin\Coupon42\CouponService:
        public: false
        autowire: true
        decorates: Plugin\Coupon42\Service\CouponService
    # 販売種目が異なっても同時購入できるようにカスタマイズ
    Eccube\Service\Cart\CartItemAllocator:
        class: Customize\Service\Cart\SaleTypeCartAllocator
        autowire: true
    Eccube\Service\Cart\CartItemComparator:
        class: Customize\Service\Cart\CartItemComparator
    # 既存Repository拡張
    Customize\Repository\Extension\NewsRepositoryExtension:
        autowire: true
        decorates: Eccube\Repository\NewsRepository
    Customize\Repository\Extension\ProductRepositoryExtension:
        autowire: true
        decorates: Eccube\Repository\ProductRepository
    Customize\Repository\Plugin\Recommend42\Extension\RecommendProductRepositoryExtension:
        autowire: true
        decorates: Plugin\Recommend42\Repository\RecommendProductRepository
    # 既存Tiwg拡張
    Customize\Twig\MobileDetectionExtension:
        arguments:
            $requestStack: '@request_stack'
        tags:
            - { name: 'twig.extension' }
    Customize\Twig\GiftCardOkuraExtension:
        arguments:
            $giftCardRepository: '@Customize\Repository\GiftCardOkuraRepository'
        tags: ['twig.extension']
    # Custom AddPointProcessor
    Customize\Service\PurchaseFlow\Processor\AddPointProcessor:
        public: false
        autowire: true
        decorates: Eccube\Service\PurchaseFlow\Processor\AddPointProcessor
    # カートサービスをカスタマイズ
    Customize\Service\CartService:
        public: false
        autowire: true
        decorates: Eccube\Service\CartService
